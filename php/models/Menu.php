<?php

class Menu {
    private $db;

    public function __construct($dbConnection) {
        $this->db = $dbConnection;
    }

    // --- Menu Methods ---

    public function getAllMenus() {
        $stmt = $this->db->query("SELECT * FROM menus ORDER BY name");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getMenuById($id) {
        $stmt = $this->db->prepare("SELECT * FROM menus WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function createMenu($name, $slug) {
        // Basic check for slug uniqueness
        if ($this->getMenuBySlug($slug)) {
            return false; // Slug already exists
        }

        $stmt = $this->db->prepare("INSERT INTO menus (name, slug) VALUES (:name, :slug)");
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        return $stmt->execute();
    }

    public function updateMenu($id, $name, $slug) {
        // Basic check for slug uniqueness, excluding current menu
        $existingMenu = $this->getMenuBySlug($slug);
        if ($existingMenu && $existingMenu['id'] != $id) {
            return false; // Slug already exists for a different menu
        }

        $stmt = $this->db->prepare("UPDATE menus SET name = :name, slug = :slug WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        return $stmt->execute();
    }

    public function deleteMenu($id) {
        // Deleting a menu should cascade delete menu items (FOREIGN KEY constraint ON DELETE CASCADE)
        $stmt = $this->db->prepare("DELETE FROM menus WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function getMenuByName($name) {
        $stmt = $this->db->prepare("SELECT * FROM menus WHERE name = :name");
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getMenuBySlug($slug) {
        $stmt = $this->db->prepare("SELECT * FROM menus WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }


    // --- Menu Item Methods ---

    public function getMenuItemsByMenuId($menuId) {
        $stmt = $this->db->prepare("SELECT * FROM menu_items WHERE menu_id = :menu_id ORDER BY position");
        $stmt->bindParam(':menu_id', $menuId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getMenuItemById($id) {
        $stmt = $this->db->prepare("SELECT * FROM menu_items WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function createMenuItem($menuId, $name, $url, $position) {
        $stmt = $this->db->prepare("INSERT INTO menu_items (menu_id, title, url, position) VALUES (:menu_id, :title, :url, :position)");
        $stmt->bindParam(':menu_id', $menuId, PDO::PARAM_INT);
        $stmt->bindParam(':title', $name, PDO::PARAM_STR);
        $stmt->bindParam(':url', $url, PDO::PARAM_STR);
        $stmt->bindParam(':position', $position, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function updateMenuItem($id, $name, $url) {
        $stmt = $this->db->prepare("UPDATE menu_items SET title = :title, url = :url WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':title', $name, PDO::PARAM_STR);
        $stmt->bindParam(':url', $url, PDO::PARAM_STR);
        return $stmt->execute();
    }

    public function deleteMenuItem($id) {
        $stmt = $this->db->prepare("DELETE FROM menu_items WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    // Method to update the order of menu items for a specific menu
    public function updateMenuItemOrder($menuId, $itemOrder) // $itemOrder is an array of item IDs in the desired order
    {
        $this->db->beginTransaction();

        try {
            $position = 1;
            $stmt = $this->db->prepare("UPDATE menu_items SET position = :position WHERE id = :id AND menu_id = :menu_id");

            foreach ($itemOrder as $itemId) {
                $stmt->bindParam(':position', $position, PDO::PARAM_INT);
                $stmt->bindParam(':id', $itemId, PDO::PARAM_INT);
                $stmt->bindParam(':menu_id', $menuId, PDO::PARAM_INT);
                $stmt->execute();
                $position++;
            }

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            // Log error
            error_log("Error updating menu item order for menu ID " . $menuId . ": " . $e->getMessage());
            return false;
        }
    }

    // Method to get the count of items for each menu
    public function getItemCountByMenuId() {
        $stmt = $this->db->query("SELECT menu_id, COUNT(*) as count FROM menu_items GROUP BY menu_id");
        $counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR); // {menu_id: count}
        return $counts;
    }
}
