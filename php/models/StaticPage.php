<?php

class StaticPage {
    private $db;
    private $uploadDir;

    public function __construct($dbConnection) {
        $this->db = $dbConnection;
        $this->uploadDir = BASE_PATH . '/public/uploads/static_pages/';
        
        // Utw<PERSON>rz katalog uploads jeśli nie istnieje
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }

    public function getAllStaticPages() {
        $stmt = $this->db->query("SELECT * FROM static_pages ORDER BY created_at DESC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getStaticPageById($id) {
        $stmt = $this->db->prepare("SELECT * FROM static_pages WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getStaticPageBySlug($slug) {
        $stmt = $this->db->prepare("SELECT * FROM static_pages WHERE slug = :slug");
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function createStaticPage($title, $slug, $content, $published) {
        $stmt = $this->db->prepare("INSERT INTO static_pages (title, slug, content, published) VALUES (:title, :slug, :content, :published)");
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->bindParam(':content', $content, PDO::PARAM_STR);
        $stmt->bindParam(':published', $published, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function updateStaticPage($id, $title, $slug, $content, $published) {
        $stmt = $this->db->prepare("UPDATE static_pages SET title = :title, slug = :slug, content = :content, published = :published, updated_at = CURRENT_TIMESTAMP WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
        $stmt->bindParam(':content', $content, PDO::PARAM_STR);
        $stmt->bindParam(':published', $published, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function deleteStaticPage($id) {
        $stmt = $this->db->prepare("DELETE FROM static_pages WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    public function updatePublishedStatus($id, $published) {
        $stmt = $this->db->prepare("UPDATE static_pages SET published = :published, updated_at = CURRENT_TIMESTAMP WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':published', $published, PDO::PARAM_INT);
        return $stmt->execute();
    }

    /**
     * Count all static pages
     * @return int Number of static pages
     */
    public function countAll() {
        $stmt = $this->db->query("SELECT COUNT(*) FROM static_pages");
        return $stmt->fetchColumn();
    }

    /**
     * Upload multiple files for a static page
     */
    public function uploadMultipleFiles($files, $staticPageId) {
        $uploadedFiles = [];
        
        // Debug: log received files
        error_log("uploadMultipleFiles called for static page ID: $staticPageId");
        error_log("Files received: " . print_r($files, true));
        
        // Dozwolone typy plików
        $allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $allowedDocumentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/zip',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
            'text/plain',
            'text/csv'
        ];
        
        $allowedTypes = array_merge($allowedImageTypes, $allowedDocumentTypes);
        $maxFileSize = 10 * 1024 * 1024; // 10MB
        
        if (!empty($files['tmp_name'])) {
            foreach ($files['tmp_name'] as $index => $tmpName) {
                if ($files['error'][$index] === UPLOAD_ERR_OK) {
                    $originalFilename = $files['name'][$index];
                    $mimeType = $files['type'][$index];
                    $fileSize = $files['size'][$index];
                    
                    // Walidacja typu pliku
                    if (!in_array($mimeType, $allowedTypes)) {
                        error_log("Nieprawidłowy typ pliku: $mimeType dla pliku $originalFilename");
                        continue;
                    }
                    
                    // Walidacja rozmiaru pliku
                    if ($fileSize > $maxFileSize) {
                        error_log("Plik zbyt duży: $fileSize bajtów dla pliku $originalFilename");
                        continue;
                    }
                    
                    // Dodatkowa walidacja przez finfo
                    $finfo = finfo_open(FILEINFO_MIME_TYPE);
                    $detectedMimeType = finfo_file($finfo, $tmpName);
                    finfo_close($finfo);
                    
                    // Sprawdź czy wykryty typ jest w dozwolonych typach
                    if (!in_array($detectedMimeType, $allowedTypes)) {
                        error_log("Niewspierany wykryty typ MIME: $detectedMimeType dla pliku $originalFilename");
                        continue;
                    }
                    
                    // Użyj wykrytego typu MIME zamiast deklarowanego
                    $mimeType = $detectedMimeType;
                    
                    $filename = uniqid() . '_' . basename($originalFilename);
                    $filepath = $this->uploadDir . $filename;
                    
                    // Determine file type based on MIME type
                    $fileType = 'attachment';
                    if (in_array($mimeType, $allowedImageTypes)) {
                        $fileType = 'image';
                    }
                    
                    if (move_uploaded_file($tmpName, $filepath)) {
                        error_log("File uploaded successfully: $filepath");
                        
                        // Save file info to database
                        $fileId = $this->saveFileToDatabase($staticPageId, $filename, $originalFilename, '/uploads/static_pages/' . $filename, $fileType, $mimeType, $fileSize);
                        
                        if ($fileId) {
                            error_log("File saved to database with ID: $fileId");
                            $uploadedFiles[] = [
                                'id' => $fileId,
                                'filename' => $filename,
                                'original_filename' => $originalFilename,
                                'filepath' => '/uploads/static_pages/' . $filename,
                                'file_type' => $fileType,
                                'mime_type' => $mimeType,
                                'file_size' => $fileSize
                            ];
                        } else {
                            error_log("Failed to save file to database: $originalFilename");
                        }
                    } else {
                        error_log("Failed to move uploaded file: $tmpName to $filepath");
                    }
                }
            }
        }
        
        return $uploadedFiles;
    }

    /**
     * Save file information to database
     */
    private function saveFileToDatabase($staticPageId, $filename, $originalFilename, $filepath, $fileType, $mimeType, $fileSize) {
        $stmt = $this->db->prepare("INSERT INTO static_page_files (static_page_id, filename, original_filename, filepath, file_type, mime_type, file_size) VALUES (:static_page_id, :filename, :original_filename, :filepath, :file_type, :mime_type, :file_size)");
        
        $stmt->bindParam(':static_page_id', $staticPageId, PDO::PARAM_INT);
        $stmt->bindParam(':filename', $filename, PDO::PARAM_STR);
        $stmt->bindParam(':original_filename', $originalFilename, PDO::PARAM_STR);
        $stmt->bindParam(':filepath', $filepath, PDO::PARAM_STR);
        $stmt->bindParam(':file_type', $fileType, PDO::PARAM_STR);
        $stmt->bindParam(':mime_type', $mimeType, PDO::PARAM_STR);
        $stmt->bindParam(':file_size', $fileSize, PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }

    /**
     * Get files for a static page
     */
    public function getStaticPageFiles($staticPageId, $fileType = null) {
        $sql = "SELECT * FROM static_page_files WHERE static_page_id = :static_page_id";
        $params = [':static_page_id' => $staticPageId];
        
        if ($fileType) {
            $sql .= " AND file_type = :file_type";
            $params[':file_type'] = $fileType;
        }
        
        $sql .= " ORDER BY position ASC, created_at ASC";
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Delete a static page file
     */
    public function deleteStaticPageFile($fileId) {
        // Get file info first
        $stmt = $this->db->prepare("SELECT * FROM static_page_files WHERE id = :id");
        $stmt->bindParam(':id', $fileId, PDO::PARAM_INT);
        $stmt->execute();
        $file = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$file) {
            return false;
        }
        
        // Delete file from filesystem
        $fullPath = BASE_PATH . '/public' . $file['filepath'];
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }
        
        // Delete from database
        $stmt = $this->db->prepare("DELETE FROM static_page_files WHERE id = :id");
        $stmt->bindParam(':id', $fileId, PDO::PARAM_INT);
        
        return $stmt->execute();
    }

    /**
     * Update file positions
     */
    public function updateFilePositions($positions) {
        try {
            $this->db->beginTransaction();
            
            foreach ($positions as $position) {
                $stmt = $this->db->prepare("UPDATE static_page_files SET position = :position WHERE id = :id");
                $stmt->bindParam(':position', $position['position'], PDO::PARAM_INT);
                $stmt->bindParam(':id', $position['id'], PDO::PARAM_INT);
                $stmt->execute();
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error updating file positions: " . $e->getMessage());
            return false;
        }
    }
}
